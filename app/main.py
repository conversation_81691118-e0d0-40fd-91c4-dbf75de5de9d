#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Three-agent collaborative workflow built on AutoGen v0.7.2 with DeepSeek model and MCP (context7).

Requirements addressed:
- Single-file implementation for the entire workflow
- Uses DeepSeek model via OpenAI-compatible client
- Loads API key from .env (DEEPSEEK_API_KEY)
- Integrates context7 MCP server to search official docs and ground agent outputs
- Three agents: author -> reviewer -> refiner

How it works (high-level):
- Start an MCP Stdio session to context7 (via npx @upstash/context7-mcp@latest)
- Create three AssistantAgent instances that share the same MCP workbench
- Step 1: Agent 1 writes initial code after searching the official docs
- Step 2: Agent 2 reviews and proposes improvements
- Step 3: Agent 3 applies the improvements and emits the final code

Run:
    python autogen_workflow.py --task "Build a CLI that fetches GitHub releases for a repo"

Env:
    echo "DEEPSEEK_API_KEY=sk-xxx" > .env

Notes:
- This file targets AutoGen AgentChat v0.7.2 API surface.
- DeepSeek is used via autogen_ext.models.openai.OpenAIChatCompletionClient with base_url set to DeepSeek's OpenAI-compatible endpoint.
- MCP tool invocation is left to the LLM via function/tool calling; agents are instructed to enumerate tools and use an appropriate search tool to fetch official docs before coding.
"""

from __future__ import annotations

import argparse
import asyncio
import json
import os
import re
import sys
from pathlib import Path
from typing import Optional

from dotenv import load_dotenv

# AutoGen AgentChat (v0.7.2) and Extensions
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.ui import Console
from autogen_ext.models.openai import OpenAIChatCompletionClient
# from autogen_ext.tools.mcp import McpWorkbench, StdioServerParams  # 暂时注释掉避免兼容性问题
from autogen_core.models import ModelInfo


def build_deepseek_client(model: str = "deepseek-chat") -> OpenAIChatCompletionClient:
    """Create an OpenAI-compatible client pointed to DeepSeek.

    - Reads DEEPSEEK_API_KEY from environment (loaded via python-dotenv)
    - Enables parallel tool calls per AutoGen 0.7.2 capability
    """
    api_key = os.getenv("DEEPSEEK_API_KEY")
    if not api_key:
        raise RuntimeError("Missing DEEPSEEK_API_KEY in environment (.env)")

    # DeepSeek OpenAI-compatible endpoint
    base_url = os.getenv("DEEPSEEK_BASE_URL", "https://api.deepseek.com/v1")

    # Create model info for DeepSeek model - 简化配置避免兼容性问题
    model_info = ModelInfo(
        family="deepseek",
        vision=False,
        function_calling=True,
        json_output=False,  # 禁用json_output避免类型问题
        structured_output=False,  # 禁用structured_output避免类型问题
    )

    client = OpenAIChatCompletionClient(
        model=model,
        api_key=api_key,
        base_url=base_url,
        parallel_tool_call=False,  # 禁用并行工具调用以避免兼容性问题
        model_info=model_info,
    )
    return client


def agent1_system(task: str) -> str:
    return f"""
You are Agent 1 (Implementer) — a senior Python engineer.
Your goals:
1) Before coding, enumerate available MCP tools, choose a search/browse tool from the context7 MCP server, and retrieve the official documentation for AutoGen v0.7.2 (AgentChat + Extensions).
2) Ground your implementation in the latest API and best practices (v0.7.2). Prefer AssistantAgent.run for one-shot tasks and MCP via workbench, leverage tool calling when needed.
3) Produce an initial, runnable Python implementation for the user's requirement, in a single file.
4) Include short inline comments where you rely on specific APIs.

User requirement:
{task}

Constraints:
- Output the implementation as a single fenced Python code block.
- Do not write long-running background servers; prefer functions/CLIs.
- If a feature is ambiguous, make a reasonable assumption and state it succinctly at the top of the file.
""".strip()


def agent2_system() -> str:
    return """
You are Agent 2 (Reviewer) — a meticulous code reviewer.
Your goals:
1) Review Agent 1's code for correctness, API compliance with AutoGen v0.7.2, clarity, performance, and security.
2) Provide a prioritized list of improvements, each with rationale.
3) Provide a minimal patch-style suggestion (diff or code snippet) where relevant.
4) Flag any missing error handling, configuration, or dependency issues.

Output format:
- Heading: Findings
- List of issues (priority high→low) with rationale
- Heading: Suggested Changes
- Minimal diffs/snippets
""".strip()


def agent3_system() -> str:
    return """
You are Agent 3 (Refiner) — a principal engineer.
Your goals:
1) Apply Agent 2's suggestions to Agent 1's code to produce a final improved version.
2) Ensure the code is runnable end-to-end and adheres to AutoGen v0.7.2.
3) Keep the implementation in a single file.
4) Output only:
   - A short changelog (bullet list)
   - The final code as a single fenced Python block
""".strip()


def _extract_final_code_block(text: str) -> Optional[str]:
    """Extract the last fenced code block from text.

    Accepts formats like ```python ... ``` or ``` ... ```.
    Returns None if not found.
    """
    if not text:
        return None
    pattern = re.compile(r"```(?:python)?\s*(.*?)```", re.DOTALL | re.IGNORECASE)
    matches = pattern.findall(text)
    if not matches:
        return None
    return matches[-1].strip()


def _load_task_from_file(path: str) -> str:
    """Load task text from a file.

    Behavior:
    - If file ends with .json: parse JSON and use first non-empty of keys ["task", "content", "description"].
    - Else: read the file as plain text.
    """
    file_path = Path(path).expanduser()
    data = file_path.read_text(encoding="utf-8")
    if file_path.suffix.lower() == ".json":
        payload = json.loads(data)
        if isinstance(payload, dict):
            for key in ("task", "content", "description"):
                value = payload.get(key)
                if isinstance(value, str) and value.strip():
                    return value.strip()
        raise ValueError("JSON task file must contain a non-empty 'task'/'content'/'description' string")
    return data.strip()


async def run_workflow(
    task: str,
    model: str = "deepseek-chat",
    save_final: Optional[str] = None,
    cycles: int = 0,
) -> None:
    """Run the 3-agent workflow end-to-end."""
    # Load .env for DEEPSEEK_API_KEY
    load_dotenv()

    # Shared model client
    model_client = build_deepseek_client(model=model)

    # 暂时不使用MCP工具以避免兼容性问题
    # 创建不带工具的简化版本Agent
    # Agent 1: implementer
    agent1 = AssistantAgent(
        name="agent_implementer",
        model_client=model_client,
        system_message=agent1_system(task),
        description="Writes initial implementation",
        model_client_stream=True,
    )

    # Agent 2: reviewer
    agent2 = AssistantAgent(
        name="agent_reviewer",
        model_client=model_client,
        system_message=agent2_system(),
        description="Reviews code and suggests prioritized improvements",
        model_client_stream=True,
    )

    # Agent 3: refiner
    agent3 = AssistantAgent(
        name="agent_refiner",
        model_client=model_client,
        system_message=agent3_system(),
        description="Applies suggestions and produces the final improved code",
        model_client_stream=True,
    )

    # Step 1 — Agent 1 drafts initial implementation
    print("\n===== Step 1: Agent 1 — Initial Implementation =====\n")
    initial_result = await agent1.run(task="Produce the initial implementation now.")
    print(initial_result)
    current_code = initial_result if isinstance(initial_result, str) else str(initial_result)

    # Optional multi-round review-revise cycles between Agent 1 and Agent 2
    for round_index in range(max(0, int(cycles))):
        print(f"\n===== Cycle {round_index + 1}: Agent 2 — Review =====\n")
        review_prompt = (
            "Review the following code for logic, performance, and security; "
            "return prioritized findings and minimal diffs/suggestions.\n\n"
            f"{current_code}\n"
        )
        review_result = await agent2.run(task=review_prompt)
        print(review_result)
        review_text = review_result if isinstance(review_result, str) else str(review_result)

        print(f"\n===== Cycle {round_index + 1}: Agent 1 — Revise =====\n")
        revise_prompt = (
            "Revise your previous code strictly according to the following review suggestions.\n"
            "Output the full revised implementation as a single fenced Python code block only.\n\n"
            "Review Suggestions:\n" + review_text + "\n\n"
            "Your previous code:\n" + current_code + "\n"
        )
        revised_result = await agent1.run(task=revise_prompt)
        print(revised_result)
        current_code = revised_result if isinstance(revised_result, str) else str(revised_result)

    # Step 3 — Agent 3 refines
    print("\n===== Step 3: Agent 3 — Final Improved Code =====\n")
    refine_prompt = (
        "Produce the final improved version of the code.\n\n"
        "Constraints: single-file output, runnable, include a short changelog first, "
        "then a single fenced Python code block for the final implementation.\n\n"
        "Latest Code (after cycles):\n" + current_code + "\n"
    )
    final_result = await agent3.run(task=refine_prompt)
    print(final_result)

    # Optionally persist the final code block to a file for convenience
    if save_final:
        final_text = final_result if isinstance(final_result, str) else str(final_result)
        code = _extract_final_code_block(final_text)
        if code:
            target_path = Path(save_final).expanduser().resolve()
            target_path.parent.mkdir(parents=True, exist_ok=True)
            target_path.write_text(code + "\n", encoding="utf-8")
            print(f"\n[Saved] Final code written to: {target_path}")
        else:
            print("\n[Warn] Could not detect a fenced Python code block in Agent 3 output; nothing saved.")


def parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(description="Three-agent AutoGen workflow with DeepSeek + MCP (context7)")
    parser.add_argument(
        "--task",
        help="User development requirement for Agent 1 to implement (free text)",
    )
    parser.add_argument(
        "--task-file",
        dest="task_file",
        help="Path to a task file (.json or .txt). If .json, expects 'task' or 'content' field.",
    )
    parser.add_argument(
        "--model",
        default=os.getenv("DEEPSEEK_MODEL", "deepseek-chat"),
        help="DeepSeek model name (default: deepseek-chat)",
    )
    parser.add_argument(
        "--save-final",
        dest="save_final",
        help="If set, save Agent 3's final fenced Python code to this filepath",
    )
    parser.add_argument(
        "--cycles",
        type=int,
        default=0,
        help="Number of review-revise cycles between Agent 1 and Agent 2 before Agent 3 finalizes",
    )
    args = parser.parse_args()
    # Validate task inputs: at least one of --task or --task-file must be provided
    if not args.task and not args.task_file:
        parser.error("One of --task or --task-file is required")
    return args


if __name__ == "__main__":
    args = parse_args()
    try:
        # Resolve task from file if provided
        resolved_task = args.task
        if args.task_file:
            resolved_task = _load_task_from_file(args.task_file)

        asyncio.run(
            run_workflow(
                task=resolved_task,
                model=args.model,
                save_final=args.save_final,
                cycles=args.cycles,
            )
        )
    except KeyboardInterrupt:
        print("Interrupted by user.")
