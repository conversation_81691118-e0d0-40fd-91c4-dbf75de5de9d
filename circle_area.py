import math
import logging

logging.basicConfig(level=logging.INFO)

def calculate_circle_area(radius):
    """
    Calculate the area of a circle given its radius.

    Args:
        radius (float or int): The radius of the circle.

    Returns:
        float: The area of the circle.

    Raises:
        ValueError: If the radius is not a finite positive number.
        TypeError: If the radius is not a number.
    """
    try:
        # Validate input type
        if not isinstance(radius, (int, float)):
            raise TypeError(f"Radius must be a number (int or float). Got: {type(radius)}")

        # Validate input value
        if radius <= 0 or math.isnan(radius) or math.isinf(radius):
            raise ValueError(f"Radius must be a finite positive number. Got: {radius}")

        # Calculate and return the area
        return math.pi * (radius ** 2)
    except (TypeError, ValueError) as e:
        logging.error(f"Error in calculate_circle_area: {e}")
        raise

# Example usage
if __name__ == "__main__":
    try:
        radius = float(input("Enter the radius of the circle: "))
        area = calculate_circle_area(radius)
        print(f"The area of the circle with radius {radius} is {area:.2f}")
    except ValueError as e:
        print(f"Error: {e}")
    except TypeError as e:
        print(f"Error: {e}")
