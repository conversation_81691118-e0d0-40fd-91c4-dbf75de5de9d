"""
Image Compression Tool (Final Version)

Features:
- Supports JPEG, PNG, WEBP formats (converts others to JPEG)
- Quality adjustment (1-100) for lossy compression
- Resizing while maintaining aspect ratio
- Parallel processing for batch operations
- Preserves EXIF metadata
- Secure path handling and validation
"""

import os
import sys
from PIL import Image
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
import argparse
from typing import Optional, <PERSON><PERSON>

def validate_paths(input_path: str, output_path: str) -> Tuple[Path, Path]:
    """Validate and normalize input/output paths"""
    input_path = Path(input_path).resolve()
    output_path = Path(output_path).resolve()
    
    if not input_path.exists():
        raise FileNotFoundError(f"Input path does not exist: {input_path}")
    if not input_path.is_file():
        raise ValueError(f"Input path is not a file: {input_path}")
    
    return input_path, output_path

def compress_image(
    input_path: str,
    output_path: str,
    quality: int = 75,
    width: Optional[int] = None,
    height: Optional[int] = None,
    format: Optional[str] = None
) -> None:
    """
    Compress and/or resize an image with validation and metadata preservation
    
    Args:
        input_path: Path to source image
        output_path: Path to save compressed image
        quality: Compression quality (1-100)
        width: Target width (maintains aspect ratio)
        height: Target height (maintains aspect ratio)
        format: Output format (JPEG/PNG/WEBP), None keeps original
    """
    input_path, output_path = validate_paths(input_path, output_path)
    
    try:
        # Verify image before processing
        with Image.open(input_path) as img:
            img.verify()
    except (IOError, SyntaxError) as e:
        raise ValueError(f"Invalid image file: {input_path}") from e

    try:
        with Image.open(input_path) as img:
            # Resize if dimensions provided
            if width or height:
                orig_width, orig_height = img.size
                if width and not height:
                    height = int((width / orig_width) * orig_height)
                elif height and not width:
                    width = int((height / orig_height) * orig_width)
                
                img = img.resize((width, height), Image.LANCZOS)
            
            # Case-insensitive format handling
            output_format = (format or img.format or 'JPEG').upper()
            if output_format not in {'JPEG', 'PNG', 'WEBP'}:
                output_format = 'JPEG'
            
            # Prepare save parameters
            save_params = {
                'format': output_format,
                'quality': quality,
                'optimize': True
            }
            
            # Preserve EXIF metadata for JPEGs if available
            if output_format == 'JPEG' and 'exif' in img.info:
                save_params['exif'] = img.info['exif']
            
            # Ensure output directory exists
            output_path.parent.mkdir(parents=True, exist_ok=True)
            
            # Handle filename collisions
            counter = 1
            original_stem = output_path.stem
            while output_path.exists():
                output_path = output_path.with_stem(f"{original_stem}_{counter}")
                counter += 1
            
            img.save(output_path, **save_params)
            
    except Image.DecompressionBombError:
        raise ValueError("Image dimensions exceed safety limit") from None
    except Exception as e:
        raise RuntimeError(f"Failed to process image: {str(e)}") from e

def batch_compress(
    input_dir: str,
    output_dir: str,
    quality: int = 75,
    width: Optional[int] = None,
    height: Optional[int] = None,
    format: Optional[str] = None,
    max_workers: int = 4
) -> None:
    """
    Parallel compression of all images in a directory
    
    Args:
        input_dir: Directory containing source images
        output_dir: Directory to save compressed images
        quality: Compression quality (1-100)
        width: Target width (optional)
        height: Target height (optional)
        format: Output format (JPEG/PNG/WEBP)
        max_workers: Maximum parallel threads
    """
    input_dir = Path(input_dir).resolve()
    output_dir = Path(output_dir).resolve()
    
    if not input_dir.exists():
        raise FileNotFoundError(f"Input directory not found: {input_dir}")
    if not input_dir.is_dir():
        raise ValueError(f"Input path is not a directory: {input_dir}")
    
    output_dir.mkdir(parents=True, exist_ok=True)
    
    supported_formats = ('.jpg', '.jpeg', '.png', '.webp')
    files_to_process = [
        f for f in input_dir.iterdir() 
        if f.suffix.lower() in supported_formats and f.is_file()
    ]
    
    if not files_to_process:
        print("No supported image files found in input directory")
        return
    
    total_files = len(files_to_process)
    processed_files = 0
    
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        futures = {
            executor.submit(
                compress_image,
                input_path=str(file),
                output_path=str(output_dir / file.name),
                quality=quality,
                width=width,
                height=height,
                format=format
            ): file.name for file in files_to_process
        }
        
        for future in as_completed(futures):
            filename = futures[future]
            try:
                future.result()
                processed_files += 1
                print(f"Processed {filename} ({processed_files}/{total_files})")
            except (IOError, PermissionError) as e:
                print(f"File operation failed for {filename}: {str(e)}")
            except ValueError as e:
                print(f"Skipping {filename}: {str(e)}")
            except Exception as e:
                print(f"Error processing {filename}: {str(e)}")
                raise

def main():
    parser = argparse.ArgumentParser(
        description="Advanced Image Compression Tool",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    parser.add_argument(
        "input_dir",
        help="Directory containing source images"
    )
    parser.add_argument(
        "output_dir",
        help="Directory to save compressed images"
    )
    parser.add_argument(
        "-q", "--quality",
        type=int,
        default=75,
        choices=range(1, 101),
        metavar="[1-100]",
        help="Compression quality (1-100)"
    )
    parser.add_argument(
        "-W", "--width",
        type=int,
        help="Target width (maintains aspect ratio)"
    )
    parser.add_argument(
        "-H", "--height",
        type=int,
        help="Target height (maintains aspect ratio)"
    )
    parser.add_argument(
        "-f", "--format",
        choices=['JPEG', 'PNG', 'WEBP'],
        help="Output format (default: keep original)"
    )
    parser.add_argument(
        "-j", "--workers",
        type=int,
        default=4,
        help="Number of parallel workers"
    )
    
    args = parser.parse_args()
    
    try:
        batch_compress(
            input_dir=args.input_dir,
            output_dir=args.output_dir,
            quality=args.quality,
            width=args.width,
            height=args.height,
            format=args.format,
            max_workers=args.workers
        )
    except Exception as e:
        print(f"Error: {str(e)}", file=sys.stderr)
        sys.exit(1)

if __name__ == "__main__":
    main()
