
# Simple Hello World function implementation using Python
# Assumption: Standard function output to console is sufficient

def hello_world():
    """Prints 'Hello, World!' to the console.
    
    Returns:
        str: The greeting message
        
    Raises:
        IOError: If output cannot be written to stdout
    """
    try:
        message = "Hello, World!"
        print(message)
        return message
    except IOError as e:
        raise IOError("Failed to write to stdout") from e

# Example usage
if __name__ == "__main__":
    hello_world()

