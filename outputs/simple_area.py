\n"""\nFinal implementation of circle area calculator with comprehensive validation.\nKey features:\n- Strict input validation\n- Complete overflow/underflow protection\n- Clear error messages\n- Robust CLI interface\n- Comprehensive test coverage\n"""\n\nimport math\nimport sys\n\ndef area_of_circle(r: float) -> float:\n    """Calculate the area of a circle given its radius.\n    \n    Args:\n        r: Radius of the circle (must be positive finite number)\n    \n    Returns:\n        Area of the circle (πr²)\n    \n    Raises:\n        ValueError: If radius is invalid (non-number, negative, or unsafe)\n    \n    Note:\n        Floating-point precision limitations may affect result accuracy\n        for very large or small radius values.\n    \n    Example:\n        >>> area_of_circle(2.0)\n        12.566370614359172\n    """\n    # Strict type checking\n    if not isinstance(r, (int, float)):\n        raise ValueError("Radius must be a numeric value (int or float)")\n    \n    # Special float cases\n    if math.isnan(r):\n        raise ValueError("Radius cannot be NaN")\n    if math.isinf(r):\n        raise ValueError("Radius cannot be infinite")\n    \n    # Value range checks\n    if r < 0:\n        raise ValueError("Radius cannot be negative")\n    \n    # Overflow/underflow protection\n    max_safe_radius = math.sqrt(sys.float_info.max / math.pi)\n    min_safe_radius = math.sqrt(sys.float_info.min / math.pi)\n    \n    if abs(r) > max_safe_radius:\n        raise ValueError("Radius too large - would cause overflow when squared")\n    if 0 < abs(r) < min_safe_radius:\n        raise ValueError("Radius too small - would cause underflow when squared")\n    \n    return math.pi * r * r\n\nif __name__ == "__main__":\n    # Enhanced CLI with retry logic\n    while True:\n        try:\n            radius_input = input("Enter circle radius (or \'q\' to quit): ").strip()\n            if radius_input.lower() == \'q\':\n                sys.exit(0)\n            if not radius_input:\n                print("Error: Empty input")\n                continue\n                \n            try:\n                radius = float(radius_input)\n            except ValueError:\n                print("Error: Please enter a valid number")\n                continue\n                \n            try:\n                area = area_of_circle(radius)\n                print(f"Area: {area:.2f}")\n                break\n            except ValueError as e:\n                print(f"Error: {e}")\n                continue\n                \n        except KeyboardInterrupt:\n            print("\\nOperation cancelled")\n            sys.exit(0)\n\n# Comprehensive test cases\ndef test_area_of_circle():\n    # Test valid inputs\n    test_cases = [\n        (1.0, math.pi),\n        (2.0, 4 * math.pi),\n        (0, 0)\n    ]\n    for radius, expected in test_cases:\n        assert math.isclose(area_of_circle(radius), expected), \\\n            f"Failed for radius={radius}"\n    \n    # Test invalid inputs\n    invalid_inputs = [\n        -1, "not a number", True, False,\n        float(\'nan\'), float(\'inf\'), 1e154, 1e-154\n    ]\n    for bad_input in invalid_inputs:\n        try:\n            area_of_circle(bad_input)\n            assert False, f"Should raise for invalid input: {bad_input}"\n        except ValueError:\n            pass\n\ntest_area_of_circle()\n
