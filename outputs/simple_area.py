
import math
import argparse
import sys
from typing import Union

def area_of_circle(r: Union[float, int]) -> float:
    """Calculate circle area from radius.
    
    Args:
        r: Radius of the circle (must be non-negative number)
    
    Returns:
        Area of the circle
        
    Raises:
        TypeError: If input is not numeric
        ValueError: If radius is negative or exceeds system limits
    """
    if not isinstance(r, (int, float)):
        raise TypeError("Radius must be numeric")
    if r < 0:
        raise ValueError("Radius cannot be negative")
    if r > sys.float_info.max / math.pi:  # Prevent overflow before calculation
        raise ValueError(f"Radius exceeds maximum valid value ({sys.float_info.max/math.pi:.1e})")
    return math.pi * r ** 2

def main():
    """Command line interface for circle area calculation."""
    parser = argparse.ArgumentParser(description='Calculate circle area')
    parser.add_argument('radius', type=float, help='Positive radius of the circle')
    parser.add_argument('--precision', type=int, default=2,
                      help='Decimal places for result (0-15, default: 2)')
    args = parser.parse_args()
    
    try:
        if args.precision < 0 or args.precision > 15:
            raise ValueError("Precision must be between 0 and 15")
        area = area_of_circle(args.radius)
        print(f"Area: {area:.{args.precision}f}")
    except ValueError as e:
        print(f"Value error: {e}", file=sys.stderr)
        sys.exit(1)
    except TypeError as e:
        print(f"Type error: {e}", file=sys.stderr)
        sys.exit(2)

def test_area_of_circle():
    """Test area calculation with various inputs including edge cases"""
    # Happy path
    assert math.isclose(area_of_circle(1), math.pi)
    assert math.isclose(area_of_circle(2.5), math.pi * 6.25)
    
    # Edge cases
    assert area_of_circle(0) == 0
    assert math.isclose(area_of_circle(1e-10), math.pi * 1e-20)
    
    # Error cases
    try:
        area_of_circle(-1)
        assert False, "Should raise for negative"
    except ValueError:
        pass
        
    try:
        area_of_circle("abc")
        assert False, "Should raise for non-numeric"
    except TypeError:
        pass
        
    try:
        area_of_circle(sys.float_info.max)
        assert False, "Should raise for very large numbers"
    except ValueError:
        pass

if __name__ == "__main__":
    if "--test" in sys.argv:
        test_area_of_circle()
        print("All tests passed")
    else:
        main()

