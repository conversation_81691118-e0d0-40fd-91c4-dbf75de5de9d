import math

def calculate_circle_area(radius):
    """
    Calculate the area of a circle given its radius.

    Args:
        radius (float or int): The radius of the circle.

    Returns:
        float: The area of the circle.

    Raises:
        TypeError: If the radius is not a float or int.
        ValueError: If the radius is negative, infinite, or NaN.
    """
    # Validate input type
    if not isinstance(radius, (int, float)):
        raise TypeError("Radius must be a float or integer.")

    # Validate input value
    if radius < 0:
        raise ValueError("Radius cannot be negative.")
    if radius in (math.inf, -math.inf):
        raise ValueError("Radius cannot be infinite.")

    # Calculate and return the area
    return math.pi * (radius ** 2)

if __name__ == "__main__":
    try:
        user_input = input("Enter the radius of the circle: ")
        radius = float(user_input)
        area = calculate_circle_area(radius)
        print(f"The area of the circle is: {area:.2f}")
    except ValueError as ve:
        print(f"Error: {ve}")
    except TypeError as te:
        print(f"Error: {te}")
    except (KeyboardInterrupt, EOFError):
        print("\
Operation cancelled by user.")

