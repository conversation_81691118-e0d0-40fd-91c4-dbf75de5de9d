import math

def calculate_circle_area(radius):
    """
    Calculate the area of a circle given its radius.

    Args:
        radius (float or int): The radius of the circle.

    Returns:
        float: The area of the circle.

    Raises:
        TypeError: If the radius is not a float or int.
        ValueError: If the radius is negative, infinite, or NaN.
    """
    # Validate input type
    if not isinstance(radius, (int, float)):
        raise TypeError("Radius must be a float or integer.")

    # Validate input value
    if radius < 0:
        raise ValueError("Radius cannot be negative.")
    if radius in (math.inf, -math.inf):
        raise ValueError("Radius cannot be infinite.")

    # Calculate and return the area
    return math.pi * (radius ** 2)

if __name__ == "__main__":
    # Test the function with different values
    test_values = [5, 3.14, 0, 10.5]
    
    for radius in test_values:
        try:
            area = calculate_circle_area(radius)
            print(f"Radius: {radius}, Area: {area:.2f}")
        except (ValueError, TypeError) as e:
            print(f"Radius: {radius}, Error: {e}")
