# Autogen DeepSeek MCP Agents (v0.7.2)

A professional, single-command, three-agent workflow built on AutoGen v0.7.2 (AgentChat), integrating the `context7` MCP server for documentation search and using DeepSeek (OpenAI-compatible) models.

## Features
- Three cooperative agents:
  - Agent 1: authors initial implementation
  - Agent 2: reviews with prioritized improvements
  - Agent 3: refines and outputs a final single-file version
- MCP integration via `@upstash/context7-mcp` for grounding in official docs
- DeepSeek model integration via OpenAI-compatible client
- Single-file workflow logic under `app/main.py`

## Project layout
```
autogen-deepseek-agents/
├─ app/
│  └─ main.py          # Three-agent workflow entry
├─ tasks/
│  └─ area_circle.json # Example task file (JSON)
├─ scripts/
├─ .env.example        # Copy to .env and set keys
├─ requirements.txt
├─ .gitignore
└─ README.md
```

## Prerequisites
- Python 3.10+
- Node + npx (for MCP stdio server)
- A DeepSeek API key
- Conda environment (optional): `autogen-deepseek`

## Setup
```bash
# (Optional) conda create -n autogen-deepseek python=3.11 -y
# (Optional) conda activate autogen-deepseek

pip install -r requirements.txt
cp .env.example .env
# edit .env to set DEEPSEEK_API_KEY
```

## Run
```bash
# Example 1: approachable, zero-dependency task (recommended)
python app/main.py --task "请用纯标准库实现一个本地文件整理器单文件CLI（organize.py），要求：1）统计扩展名数量与总大小并写 report.json；2）--group 按扩展名移动到子文件夹（images/videos/docs/others）；3）--dry-run 试运行；4）--rename PATTERN 批量重命名支持 {index}/{stem}/{ext}/{date}，冲突自动加序号；5）--min-size/--max-size 与 --include/--exclude 过滤；6）单文件实现（argparse、函数化），含少量可运行测试示例；7）顶端有用法示例；8）健壮边界处理与简洁注释。先给出完整初版，再根据审查意见优化并给出最终版。" --save-final ./outputs/organize.py

# Example 2: use a task file (recommended for long tasks)
python app/main.py --task-file ./tasks/area_circle.json --cycles 2 --save-final ./outputs/area.py

# Example 3: 多轮交互的“计算圆面积”超简示例（理解来回审查-修订）
python app/main.py --task "请实现一个计算圆面积的函数 area_of_circle(r: float) -> float，并给出简短CLI示例（从命令行读取半径并输出面积），注意：处理非法输入与边界（负数、非数字、极大/极小值），并给出最小可运行测试片段。先初版再最终版。" --cycles 2 --save-final ./outputs/area.py
```
- Agent 1 will search the official AutoGen docs via MCP and produce a first draft.
- Agent 2 will review and suggest improvements.
- Agent 3 will produce a final improved version and changelog.

## Notes
- This workflow sets `parallel_tool_call=True` (a new option in 0.7.2) in the model client.
- The MCP server is launched via `npx -y @upstash/context7-mcp@latest`. Lock to a version if needed.
- If your DeepSeek endpoint differs, set `DEEPSEEK_BASE_URL` in `.env`.
- You can add `--save-final <path>` to automatically extract and persist the final fenced Python code emitted by Agent 3.
- You can add `--cycles N` to let Agent 1 and Agent 2 iterate N rounds of review→revise before Agent 3 finalizes. This helps beginners see how feedback loops improve the solution.

### 参数说明（详解）
- `--task`：直接在命令行传入自然语言需求文本。适合简短需求。
- `--task-file`：从文件加载任务文本；
  - `.json` 文件：读取 `task`/`content`/`description` 字段（字符串）。
  - 其他文本文件：按纯文本读取。
  - 适合较长、结构化的任务描述，便于复用与版本管理。
- `--cycles`：设置 Agent1 与 Agent2 的“撰写→审查→改写”循环轮数（默认 0）。
  - 流程：Agent1 先写初版 → 每一轮中 Agent2 审查并给出改进建议 → Agent1 按建议改写。
  - 价值：帮助你观察反馈闭环如何逐步改进同一份代码。
- `--save-final`：把 Agent3 输出中的“最终围栏代码块”自动保存到指定路径。
  - 便于直接落盘运行或纳入代码仓库。

## Troubleshooting
- If MCP fails to launch, ensure Node and npx are available: `node -v && npx -v`.
- If the model call fails, verify network access and `DEEPSEEK_API_KEY`.
- For verbose debugging, you can temporarily decrease `max_tool_iterations` or remove streaming in `main.py`.

## License
MIT (for this scaffold). AutoGen and MCP servers follow their respective licenses.
